<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'لوحة التحكم - نظام حكيم' ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%23007BFF'/><path d='M30 25c-8 0-15 7-15 15v10c0 3 2 5 5 5s5-2 5-5v-10c0-3 2-5 5-5s5 2 5 5v25c0 8 7 15 15 15s15-7 15-15v-25c0-3 2-5 5-5s5 2 5 5v10c0 3 2 5 5 5s5-2 5-5v-10c0-8-7-15-15-15-3 0-6 1-8 3-2-2-5-3-8-3z' fill='white'/><circle cx='50' cy='75' r='8' fill='white'/></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%23007BFF'/><path d='M30 25c-8 0-15 7-15 15v10c0 3 2 5 5 5s5-2 5-5v-10c0-3 2-5 5-5s5 2 5 5v25c0 8 7 15 15 15s15-7 15-15v-25c0-3 2-5 5-5s5 2 5 5v10c0 3 2 5 5 5s5-2 5-5v-10c0-8-7-15-15-15-3 0-6 1-8 3-2-2-5-3-8-3z' fill='white'/><circle cx='50' cy='75' r='8' fill='white'/></svg>">
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007BFF;
            --primary-sky-blue: #87CEEB;
            --primary-white: #FFFFFF;
            --primary-black: #333333;
            --light-gray: #F8F9FA;
            --border-color: #E0E0E0;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-hover: 0 4px 20px rgba(0,0,0,0.15);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--light-gray);
            color: var(--primary-black);
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-sky-blue) 100%);
            color: var(--primary-white);
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
            overflow-y: auto;
            max-height: 100vh;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 15px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            color: var(--primary-white);
            transform: translateX(-3px);
            border-color: rgba(255,255,255,0.2);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-white);
            color: var(--primary-blue);
            font-weight: 600;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .main-content {
            margin-right: 250px;
            padding: 25px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-white);
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            border-radius: 10px;
            padding: 15px 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 25px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-sky-blue) 100%);
            color: var(--primary-white);
            border-radius: 0 !important;
            border: none;
            padding: 20px;
            font-weight: 600;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-sky-blue) 100%);
            color: var(--primary-white);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .stats-card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-3px);
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stats-card .stats-icon {
            opacity: 0.8;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-sky-blue) 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3 0%, #5f9ea0 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .btn-success {
            background-color: var(--primary-sky-blue);
            border-color: var(--primary-sky-blue);
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-success:hover {
            background-color: #5f9ea0;
            border-color: #5f9ea0;
            transform: translateY(-2px);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .table th {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-sky-blue) 100%);
            color: var(--primary-white);
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .badge {
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.8rem;
        }

        .alert {
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            font-weight: 500;
            box-shadow: var(--shadow);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid var(--border-color);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .sidebar-brand {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .sidebar-brand h4 {
            font-weight: 700;
            font-size: 1.5rem;
            margin: 0;
        }

        /* تنسيق زر القائمة للموبايل */
        .mobile-menu-btn {
            border: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            background: var(--primary-white);
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,123,255,0.1);
        }

        .mobile-menu-btn:hover {
            background: var(--primary-blue);
            color: var(--primary-white);
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(0,123,255,0.2);
        }

        .mobile-menu-btn:focus {
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        /* Overlay للموبايل */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            animation: fadeIn 0.3s ease forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-right: -250px;
            }

            .sidebar.show {
                margin-right: 0;
            }

            .main-content {
                margin-right: 0;
                padding: 15px;
            }

            .stats-card {
                margin-bottom: 15px;
                padding: 20px;
            }

            .stats-card .stats-number {
                font-size: 2rem;
            }

            /* تحسين تنسيق الهيدر للموبايل */
            .navbar {
                padding: 10px 15px;
                background: var(--primary-white);
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border-bottom: 1px solid var(--border-color);
            }

            .navbar-brand {
                font-size: 1.1rem;
            }

            .navbar-nav .nav-item .btn {
                font-size: 0.85rem;
                padding: 6px 12px;
            }

            .dropdown-menu {
                font-size: 0.9rem;
            }

            /* تحسين ترتيب العناصر للموبايل */
            .container-fluid {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .order-1 {
                order: 1;
            }

            .order-2 {
                order: 2;
            }

            .navbar-nav {
                order: 3;
            }

            /* تحسين أيقونة الإشعارات للموبايل */
            .nav-item .nav-link {
                padding: 8px 10px;
            }

            .nav-item .nav-link i {
                font-size: 1.1rem;
            }

            /* تحسين زر تسجيل الخروج للموبايل */
            .nav-item .btn-outline-danger {
                border-width: 1px;
                font-size: 0.8rem;
                padding: 5px 10px;
            }

            .nav-item .btn-outline-danger i {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand">
            <h4 class="text-center">
                <i class="fas fa-stethoscope me-2"></i>
                نظام حكيم
            </h4>
        </div>

        <div class="px-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'appointments.php' ? 'active' : '' ?>" href="appointments.php">
                        <i class="fas fa-calendar-alt me-2"></i>
                        المواعيد
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'patients.php' ? 'active' : '' ?>" href="patients.php">
                        <i class="fas fa-users me-2"></i>
                        المرضى
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'sessions.php' ? 'active' : '' ?>" href="sessions.php">
                        <i class="fas fa-user-md me-2"></i>
                        الجلسات الطبية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'prescriptions.php' ? 'active' : '' ?>" href="prescriptions.php">
                        <i class="fas fa-prescription-bottle-alt me-2"></i>
                        الوصفات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'invoices.php' ? 'active' : '' ?>" href="invoices.php">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        الفواتير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : '' ?>" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : '' ?>" href="settings.php">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات
                    </a>
                </li>
            </ul>
        </div>

        <!-- User Info -->
        <div class="mt-auto p-3 border-top border-light">
            <div class="d-flex align-items-center">
                <div class="avatar me-3">
                    <div class="bg-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user text-primary"></i>
                    </div>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-bold text-white"><?= $_SESSION['user_name'] ?? 'المستخدم' ?></div>
                    <small class="text-light opacity-75">طبيب</small>
                </div>
                <div class="dropdown">
                    <button class="btn btn-link text-white p-0" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="settings.php?action=profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php?action=password">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container-fluid">
                <!-- Mobile Menu Button (Left Side for Arabic) -->
                <div class="d-flex align-items-center order-1 d-md-none">
                    <button class="btn btn-outline-primary mobile-menu-btn" type="button" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- Logo (Center on Mobile, Right on Desktop) -->
                <div class="navbar-brand d-flex align-items-center order-2 order-md-1 mx-auto mx-md-0">
                    <div class="logo-container me-2">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white"
                             style="width: 40px; height: 40px;">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                    </div>
                    <span class="fw-bold text-primary d-none d-sm-inline">نظام حكيم</span>
                </div>

                <!-- Right Side Items -->
                <div class="navbar-nav ms-auto d-flex flex-row align-items-center order-3">
                    <!-- Notifications -->
                    <div class="nav-item dropdown me-3">
                        <a class="nav-link dropdown-toggle position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell fa-lg"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?= isset($notifications) ? count($notifications) : 0 ?>
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">الإشعارات</h6></li>
                            <?php if (empty($notifications)): ?>
                                <li><span class="dropdown-item text-muted">لا توجد إشعارات جديدة</span></li>
                            <?php else: ?>
                                <?php foreach ($notifications as $notification): ?>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-bell me-2 text-primary"></i><?= htmlspecialchars($notification['title']) ?>
                                    </a></li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="notifications.php">عرض جميع الإشعارات</a></li>
                        </ul>
                    </div>

                    <!-- Logout Button -->
                    <div class="nav-item">
                        <a class="btn btn-outline-danger btn-sm" href="logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid">
            <?php if (isset($_SESSION['notification'])): ?>
                <div class="alert alert-<?= $_SESSION['notification']['type'] ?> alert-dismissible fade show" role="alert">
                    <?= $_SESSION['notification']['message'] ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['notification']); ?>
            <?php endif; ?>
            
            <?= $content ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.toggle('show');

            // إضافة/إزالة overlay للموبايل
            if (sidebar.classList.contains('show')) {
                if (!overlay) {
                    const newOverlay = document.createElement('div');
                    newOverlay.id = 'sidebar-overlay';
                    newOverlay.className = 'sidebar-overlay d-md-none';
                    newOverlay.onclick = toggleSidebar;
                    document.body.appendChild(newOverlay);
                }
            } else {
                if (overlay) {
                    overlay.remove();
                }
            }
        }

        // إغلاق السايدبار عند النقر خارجه على الموبايل
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const mobileBtn = document.querySelector('.mobile-menu-btn');

            if (window.innerWidth <= 768 &&
                sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                !mobileBtn.contains(e.target)) {
                toggleSidebar();
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                }
            });
        }, 5000);
    </script>
</body>
</html>
